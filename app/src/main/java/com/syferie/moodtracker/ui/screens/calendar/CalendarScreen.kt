package com.syferie.moodtracker.ui.screens.calendar

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.animation.AnimationConstants
import com.syferie.moodtracker.ui.animation.pressAnimation
import com.syferie.moodtracker.ui.theme.MoodColors
import com.syferie.moodtracker.ui.viewmodel.MoodViewModel
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    moodViewModel: MoodViewModel
) {
    val selectedDate by moodViewModel.selectedDate.collectAsStateWithLifecycle()
    // 移除对allMoods的订阅，减少不必要的重组

    var currentMonth by remember { mutableStateOf(YearMonth.now()) }

    // 获取当前月份的心情记录 - 使用更高效的数据流
    val monthMoods by moodViewModel.getMoodsForMonth(
        currentMonth.year,
        currentMonth.monthValue
    ).collectAsStateWithLifecycle(initialValue = emptyList())
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 月份导航
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { currentMonth = currentMonth.minusMonths(1) }
                ) {
                    Icon(Icons.AutoMirrored.Filled.KeyboardArrowLeft, contentDescription = "上个月")
                }
                
                Text(
                    text = currentMonth.format(DateTimeFormatter.ofPattern("yyyy年MM月")),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                IconButton(
                    onClick = { currentMonth = currentMonth.plusMonths(1) }
                ) {
                    Icon(Icons.AutoMirrored.Filled.KeyboardArrowRight, contentDescription = "下个月")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                Text(
                    text = day,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 日历网格
        CalendarGrid(
            currentMonth = currentMonth,
            selectedDate = selectedDate,
            monthMoods = monthMoods,
            onDateSelected = { date ->
                moodViewModel.selectDate(date)
            }
        )
        

    }
}

@Composable
private fun CalendarGrid(
    currentMonth: YearMonth,
    selectedDate: LocalDate,
    monthMoods: List<com.syferie.moodtracker.data.model.MoodEntity>,
    onDateSelected: (LocalDate) -> Unit
) {
    // 使用remember缓存日历天数计算，避免重复计算
    val calendarDays = remember(currentMonth) {
        val firstDayOfMonth = currentMonth.atDay(1)
        val lastDayOfMonth = currentMonth.atEndOfMonth()
        val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 0 = Sunday

        val days = mutableListOf<LocalDate?>()

        // 添加前面的空白天数
        repeat(firstDayOfWeek) {
            days.add(null)
        }

        // 添加当月的所有天数
        for (day in 1..lastDayOfMonth.dayOfMonth) {
            days.add(currentMonth.atDay(day))
        }

        days
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        verticalArrangement = Arrangement.spacedBy(4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(calendarDays) { date ->
            CalendarDayItem(
                date = date,
                isSelected = date == selectedDate,
                mood = date?.let { d -> monthMoods.find { it.date == d } },
                onDateSelected = { date?.let(onDateSelected) }
            )
        }
    }
}

@Composable
private fun CalendarDayItem(
    date: LocalDate?,
    isSelected: Boolean,
    mood: com.syferie.moodtracker.data.model.MoodEntity?,
    onDateSelected: () -> Unit
) {
    // 只有选中状态才有背景色，心情不影响背景
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
        } else {
            Color.Transparent
        },
        animationSpec = tween(
            durationMillis = AnimationConstants.NORMAL_ANIMATION,
            easing = AnimationConstants.EaseInOutSine
        ),
        label = "calendarDayBackground"
    )

    val textColor by animateColorAsState(
        targetValue = when {
            isSelected -> MaterialTheme.colorScheme.primary
            date == LocalDate.now() -> MaterialTheme.colorScheme.primary
            else -> MaterialTheme.colorScheme.onSurface
        },
        animationSpec = tween(
            durationMillis = AnimationConstants.NORMAL_ANIMATION,
            easing = AnimationConstants.EaseInOutSine
        ),
        label = "calendarDayText"
    )

    val context = LocalContext.current

    // 获取心情图标资源ID
    val moodImageRes = remember(mood) {
        mood?.let { moodEntity ->
            val moodLevel = MoodLevel.fromValue(moodEntity.mood)
            context.resources.getIdentifier(
                "mood_${moodLevel.iconRes}",
                "drawable",
                context.packageName
            )
        }
    }
    
    // 重构为垂直布局：日期在上，表情在下
    Column(
        modifier = Modifier
            .size(width = 48.dp, height = 64.dp) // 调整尺寸以容纳垂直布局
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable(enabled = date != null) { onDateSelected() }
            .pressAnimation(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        if (date != null) {
            // 日期数字在上方
            Text(
                text = date.dayOfMonth.toString(),
                color = textColor,
                fontSize = 16.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )

            Spacer(modifier = Modifier.height(4.dp))

            // 心情图标在下方，预留固定空间
            Box(
                modifier = Modifier.size(24.dp),
                contentAlignment = Alignment.Center
            ) {
                mood?.let {
                    if (moodImageRes != null && moodImageRes != 0) {
                        Image(
                            painter = painterResource(id = moodImageRes),
                            contentDescription = MoodLevel.fromValue(it.mood).description,
                            modifier = Modifier.size(24.dp),
                            contentScale = ContentScale.Fit
                        )
                    } else {
                        // 备用方案：显示emoji
                        Text(
                            text = MoodLevel.fromValue(it.mood).emoji,
                            fontSize = 16.sp
                        )
                    }
                }
                // 如果没有心情记录，这个空间保持空白
            }
        }
    }
}


