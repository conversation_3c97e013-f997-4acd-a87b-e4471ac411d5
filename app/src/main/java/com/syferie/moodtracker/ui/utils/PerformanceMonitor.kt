package com.syferie.moodtracker.ui.utils

import android.util.Log
import androidx.compose.runtime.*
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

/**
 * 性能监控工具，用于测量组件重组和渲染性能
 */
object PerformanceMonitor {
    const val TAG = "PerformanceMonitor" // 改为public，因为inline函数需要访问
    
    /**
     * 测量组件重组次数
     */
    @Composable
    fun TrackRecomposition(
        tag: String,
        content: @Composable () -> Unit
    ) {
        val recompositionCount = remember { mutableStateOf(0) }
        
        SideEffect {
            recompositionCount.value++
            Log.d(TAG, "$tag 重组次数: ${recompositionCount.value}")
        }
        
        content()
    }
    
    /**
     * 测量页面生命周期性能
     */
    @Composable
    fun TrackLifecycle(
        screenName: String,
        lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
    ) {
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                val timestamp = System.currentTimeMillis()
                when (event) {
                    Lifecycle.Event.ON_CREATE -> {
                        Log.d(TAG, "$screenName 创建时间: $timestamp")
                    }
                    Lifecycle.Event.ON_START -> {
                        Log.d(TAG, "$screenName 开始时间: $timestamp")
                    }
                    Lifecycle.Event.ON_RESUME -> {
                        Log.d(TAG, "$screenName 恢复时间: $timestamp")
                    }
                    Lifecycle.Event.ON_PAUSE -> {
                        Log.d(TAG, "$screenName 暂停时间: $timestamp")
                    }
                    Lifecycle.Event.ON_STOP -> {
                        Log.d(TAG, "$screenName 停止时间: $timestamp")
                    }
                    Lifecycle.Event.ON_DESTROY -> {
                        Log.d(TAG, "$screenName 销毁时间: $timestamp")
                    }
                    else -> {}
                }
            }
            
            lifecycleOwner.lifecycle.addObserver(observer)
            
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }
    
    /**
     * 测量函数执行时间
     */
    inline fun <T> measureTime(tag: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val endTime = System.currentTimeMillis()
        Log.d(TAG, "$tag 执行时间: ${endTime - startTime}ms")
        return result
    }
    
    /**
     * 测量导航切换时间
     */
    fun logNavigationTime(from: String, to: String) {
        val timestamp = System.currentTimeMillis()
        Log.d(TAG, "导航从 $from 到 $to，时间: $timestamp")
    }

    /**
     * 测量导航开始时间
     */
    fun logNavigationStart(from: String, to: String) {
        val timestamp = System.currentTimeMillis()
        Log.d(TAG, "导航开始: $from → $to，时间: $timestamp")
    }

    /**
     * 测量导航完成时间
     */
    fun logNavigationComplete(to: String) {
        val timestamp = System.currentTimeMillis()
        Log.d(TAG, "导航完成: $to，时间: $timestamp")
    }
}

/**
 * 用于包装需要性能监控的Composable
 */
@Composable
fun PerformanceWrapper(
    tag: String,
    enableRecompositionTracking: Boolean = true,
    enableLifecycleTracking: Boolean = true,
    content: @Composable () -> Unit
) {
    if (enableLifecycleTracking) {
        PerformanceMonitor.TrackLifecycle(screenName = tag)
    }
    
    if (enableRecompositionTracking) {
        PerformanceMonitor.TrackRecomposition(tag = tag) {
            content()
        }
    } else {
        content()
    }
}
