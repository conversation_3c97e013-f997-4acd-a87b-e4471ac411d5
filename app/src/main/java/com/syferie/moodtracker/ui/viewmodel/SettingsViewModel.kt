package com.syferie.moodtracker.ui.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.syferie.moodtracker.data.export.DataExporter
import com.syferie.moodtracker.data.export.ExportFormat
import com.syferie.moodtracker.data.repository.MoodRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class SettingsUiState(
    val isLoading: Boolean = false,
    val notificationEnabled: Boolean = false,
    val darkTheme: Boolean = false,
    val dynamicColor: Boolean = true,
    val exportInProgress: Boolean = false,
    val exportSuccess: Boolean = false,
    val errorMessage: String? = null,
    val totalRecords: Int = 0
)

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val moodRepository: MoodRepository,
    private val dataExporter: DataExporter
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    // 获取总记录数
    val totalRecords: StateFlow<Int> = moodRepository.getAllMoods()
        .map { it.size }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = 0
        )
    
    init {
        // 监听总记录数变化
        viewModelScope.launch {
            totalRecords.collect { count ->
                _uiState.value = _uiState.value.copy(totalRecords = count)
            }
        }
    }
    
    fun toggleNotification(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(notificationEnabled = enabled)
        // TODO: 实现通知设置逻辑
    }
    
    fun toggleDarkTheme(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(darkTheme = enabled)
        // TODO: 实现主题切换逻辑
    }
    
    fun toggleDynamicColor(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(dynamicColor = enabled)
        // TODO: 实现动态颜色切换逻辑
    }
    
    fun exportData(context: Context, format: ExportFormat) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    exportInProgress = true,
                    exportSuccess = false,
                    errorMessage = null
                )
                
                val moods = moodRepository.getAllMoods().first()
                val result = dataExporter.exportMoodData(context, moods, format)
                
                if (result.success) {
                    _uiState.value = _uiState.value.copy(
                        exportInProgress = false,
                        exportSuccess = true
                    )
                    
                    // 自动分享文件
                    result.filePath?.let { path ->
                        dataExporter.shareExportedFile(context, path)
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        exportInProgress = false,
                        errorMessage = result.error ?: "导出失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    exportInProgress = false,
                    errorMessage = "导出失败: ${e.message}"
                )
            }
        }
    }
    
    fun generateReport(context: Context) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    exportInProgress = true,
                    exportSuccess = false,
                    errorMessage = null
                )
                
                val moods = moodRepository.getAllMoods().first()
                val result = dataExporter.generateStatisticsReport(context, moods)
                
                if (result.success) {
                    _uiState.value = _uiState.value.copy(
                        exportInProgress = false,
                        exportSuccess = true
                    )
                    
                    // 自动分享报告
                    result.filePath?.let { path ->
                        dataExporter.shareExportedFile(context, path)
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        exportInProgress = false,
                        errorMessage = result.error ?: "生成报告失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    exportInProgress = false,
                    errorMessage = "生成报告失败: ${e.message}"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(exportSuccess = false)
    }
    
    fun deleteAllData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // TODO: 实现删除所有数据的功能
                // 需要在 Repository 中添加相应方法
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "删除数据失败: ${e.message}"
                )
            }
        }
    }
}
