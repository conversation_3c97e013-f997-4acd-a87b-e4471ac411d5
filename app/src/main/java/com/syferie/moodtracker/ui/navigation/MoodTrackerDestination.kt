package com.syferie.moodtracker.ui.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Face
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Info
import androidx.compose.ui.graphics.vector.ImageVector

enum class MoodTrackerDestination(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    DAILY_ENTRY("daily_entry", "今日心情", Icons.Default.Face),
    CALENDAR("calendar", "日历", Icons.Default.DateRange),
    STATS("stats", "统计", Icons.Default.Info),
    SETTINGS("settings", "设置", Icons.Default.Settings)
}
