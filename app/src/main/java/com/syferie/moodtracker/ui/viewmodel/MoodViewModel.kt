package com.syferie.moodtracker.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.syferie.moodtracker.data.model.MoodEntity
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.data.repository.MoodRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class MoodViewModel @Inject constructor(
    private val moodRepository: MoodRepository
) : ViewModel() {

    private val _selectedDate = MutableStateFlow(LocalDate.now())
    val selectedDate: StateFlow<LocalDate> = _selectedDate.asStateFlow()

    private val _currentMood = MutableStateFlow<MoodEntity?>(null)
    val currentMood: StateFlow<MoodEntity?> = _currentMood.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 获取所有心情记录 - 使用Lazily启动策略减少不必要的数据流
    val allMoods: StateFlow<List<MoodEntity>> = moodRepository.getAllMoods()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Lazily, // 改为Lazily，只有在被订阅时才开始
            initialValue = emptyList()
        )
    
    init {
        // 监听选中日期变化，加载对应的心情记录
        viewModelScope.launch {
            selectedDate.collect { date ->
                loadMoodForDate(date)
            }
        }
    }
    
    fun selectDate(date: LocalDate) {
        _selectedDate.value = date
    }
    
    private suspend fun loadMoodForDate(date: LocalDate) {
        try {
            _isLoading.value = true
            val mood = moodRepository.getMoodByDate(date)
            _currentMood.value = mood
        } catch (e: Exception) {
            _errorMessage.value = "加载心情记录失败: ${e.message}"
        } finally {
            _isLoading.value = false
        }
    }
    
    fun saveMood(moodLevel: MoodLevel, note: String? = null) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val moodEntity = MoodEntity(
                    date = selectedDate.value,
                    mood = moodLevel.value,
                    note = note?.takeIf { it.isNotBlank() }
                )
                moodRepository.insertMood(moodEntity)
                _currentMood.value = moodEntity
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "保存心情记录失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateMood(moodLevel: MoodLevel, note: String? = null) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val moodEntity = MoodEntity(
                    date = selectedDate.value,
                    mood = moodLevel.value,
                    note = note?.takeIf { it.isNotBlank() }
                )
                moodRepository.updateMood(moodEntity)
                _currentMood.value = moodEntity
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "更新心情记录失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteMood() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                moodRepository.deleteMoodByDate(selectedDate.value)
                _currentMood.value = null
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "删除心情记录失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    fun getMoodsForMonth(year: Int, month: Int): Flow<List<MoodEntity>> {
        val startDate = LocalDate.of(year, month, 1)
        val endDate = startDate.withDayOfMonth(startDate.lengthOfMonth())
        return moodRepository.getMoodsBetweenDates(startDate, endDate)
    }
}
