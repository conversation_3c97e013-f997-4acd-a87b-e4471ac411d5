package com.syferie.moodtracker.ui.screens.stats

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.components.MoodDistributionChart
import com.syferie.moodtracker.ui.components.MoodTrendChart
import com.syferie.moodtracker.ui.utils.PerformanceMonitor
import com.syferie.moodtracker.ui.utils.PerformanceWrapper
import com.syferie.moodtracker.ui.viewmodel.MoodViewModel
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun StatsScreen(
    moodViewModel: MoodViewModel
) {
    // 使用性能监控包装器
    PerformanceWrapper(
        tag = "StatsScreen",
        enableRecompositionTracking = true,
        enableLifecycleTracking = true
    ) {
        // 记录页面开始加载
        LaunchedEffect(Unit) {
            PerformanceMonitor.logNavigationComplete("StatsScreen")
        }

        val allMoods by moodViewModel.allMoods.collectAsStateWithLifecycle()

        // 使用remember缓存计算结果，避免重复计算
        val moodCounts = remember(allMoods) {
            PerformanceMonitor.measureTime("StatsScreen-MoodCounts计算") {
                MoodLevel.values().associateWith { level ->
                    allMoods.count { it.mood == level.value }
                }
            }
        }

        val recentTrend = remember(allMoods) {
            PerformanceMonitor.measureTime("StatsScreen-RecentTrend计算") {
                allMoods
                    .sortedBy { it.date }
                    .takeLast(14) // 最近14天
                    .map { mood ->
                        mood.date.format(DateTimeFormatter.ofPattern("MM/dd")) to mood.mood
                    }
            }
        }

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    text = "心情统计",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            item {
                OverallStatsCard(allMoods = allMoods)
            }

            item {
                MoodDistributionChart(moodCounts = moodCounts)
            }

            item {
                MoodTrendChart(moodHistory = recentTrend)
            }

            item {
                RecentTrendCard(allMoods = allMoods)
            }
        }
    }
}

@Composable
private fun OverallStatsCard(
    allMoods: List<com.syferie.moodtracker.data.model.MoodEntity>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "总体统计",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "总打卡天数",
                    value = allMoods.size.toString()
                )
                
                if (allMoods.isNotEmpty()) {
                    val avgMood = allMoods.map { it.mood }.average()
                    StatItem(
                        label = "平均心情",
                        value = String.format("%.1f", avgMood)
                    )
                    
                    val streak = calculateCurrentStreak(allMoods)
                    StatItem(
                        label = "连续打卡",
                        value = "${streak}天"
                    )
                }
            }
        }
    }
}

@Composable
private fun MoodDistributionCard(
    allMoods: List<com.syferie.moodtracker.data.model.MoodEntity>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "心情分布",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (allMoods.isNotEmpty()) {
                MoodLevel.values().forEach { moodLevel ->
                    val count = allMoods.count { it.mood == moodLevel.value }
                    val percentage = (count.toFloat() / allMoods.size * 100).toInt()
                    
                    MoodDistributionItem(
                        moodLevel = moodLevel,
                        count = count,
                        percentage = percentage
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                }
            } else {
                Text(
                    text = "暂无数据",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun RecentTrendCard(
    allMoods: List<com.syferie.moodtracker.data.model.MoodEntity>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "最近趋势",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val recentMoods = allMoods
                .sortedByDescending { it.date }
                .take(7)
            
            if (recentMoods.isNotEmpty()) {
                recentMoods.forEach { mood ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = mood.date.toString(),
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = MoodLevel.fromValue(mood.mood).emoji,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = MoodLevel.fromValue(mood.mood).description,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            } else {
                Text(
                    text = "暂无数据",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun MoodDistributionItem(
    moodLevel: MoodLevel,
    count: Int,
    percentage: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = moodLevel.emoji,
            style = MaterialTheme.typography.bodyLarge
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = moodLevel.description,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = "${count}次 (${percentage}%)",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

private fun calculateCurrentStreak(allMoods: List<com.syferie.moodtracker.data.model.MoodEntity>): Int {
    if (allMoods.isEmpty()) return 0
    
    val sortedMoods = allMoods.sortedByDescending { it.date }
    var streak = 0
    var currentDate = LocalDate.now()
    
    for (mood in sortedMoods) {
        if (mood.date == currentDate) {
            streak++
            currentDate = currentDate.minusDays(1)
        } else {
            break
        }
    }
    
    return streak
}
