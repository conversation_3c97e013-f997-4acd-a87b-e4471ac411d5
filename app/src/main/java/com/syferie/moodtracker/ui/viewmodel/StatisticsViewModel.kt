package com.syferie.moodtracker.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.syferie.moodtracker.data.repository.MoodStatistics
import com.syferie.moodtracker.data.repository.StatisticsRepository
import com.syferie.moodtracker.data.repository.WeeklyStats
import com.syferie.moodtracker.data.repository.MonthlyStats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import java.time.LocalDate
import javax.inject.Inject

enum class StatsPeriod {
    WEEK, MONTH, YEAR, ALL_TIME
}

data class StatsUiState(
    val isLoading: Boolean = false,
    val selectedPeriod: StatsPeriod = StatsPeriod.ALL_TIME,
    val statistics: MoodStatistics? = null,
    val weeklyStats: List<WeeklyStats> = emptyList(),
    val monthlyStats: List<MonthlyStats> = emptyList(),
    val errorMessage: String? = null
)

@HiltViewModel
class StatisticsViewModel @Inject constructor(
    private val statisticsRepository: StatisticsRepository
) : ViewModel() {
    
    private val _selectedPeriod = MutableStateFlow(StatsPeriod.ALL_TIME)
    val selectedPeriod: StateFlow<StatsPeriod> = _selectedPeriod.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    private val _errorMessage = MutableStateFlow<String?>(null)
    
    // 总体统计数据 - 使用Lazily启动策略
    val statistics: StateFlow<MoodStatistics?> = statisticsRepository.getMoodStatistics()
        .catch { e ->
            _errorMessage.value = "加载统计数据失败: ${e.message}"
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Lazily, // 只有在被订阅时才开始
            initialValue = null
        )

    // 周统计数据 - 使用Lazily启动策略
    val weeklyStats: StateFlow<List<WeeklyStats>> = statisticsRepository
        .getWeeklyStats(LocalDate.now().minusWeeks(7))
        .catch { e ->
            _errorMessage.value = "加载周统计失败: ${e.message}"
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Lazily,
            initialValue = emptyList()
        )

    // 月统计数据 - 使用Lazily启动策略
    val monthlyStats: StateFlow<List<MonthlyStats>> = statisticsRepository
        .getMonthlyStats(LocalDate.now().year)
        .catch { e ->
            _errorMessage.value = "加载月统计失败: ${e.message}"
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Lazily,
            initialValue = emptyList()
        )
    
    // 组合UI状态
    val uiState: StateFlow<StatsUiState> = combine(
        _isLoading,
        _selectedPeriod,
        statistics,
        weeklyStats,
        monthlyStats,
        _errorMessage
    ) { flows ->
        val isLoading = flows[0] as Boolean
        val period = flows[1] as StatsPeriod
        val stats = flows[2] as MoodStatistics?
        @Suppress("UNCHECKED_CAST")
        val weekly = flows[3] as List<WeeklyStats>
        @Suppress("UNCHECKED_CAST")
        val monthly = flows[4] as List<MonthlyStats>
        val error = flows[5] as String?

        StatsUiState(
            isLoading = isLoading,
            selectedPeriod = period,
            statistics = stats,
            weeklyStats = weekly,
            monthlyStats = monthly,
            errorMessage = error
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = StatsUiState()
    )
    
    fun selectPeriod(period: StatsPeriod) {
        _selectedPeriod.value = period
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    // 获取洞察信息
    fun getMoodInsights(): Flow<List<String>> {
        return statistics.map { stats ->
            generateInsights(stats)
        }
    }
    
    private fun generateInsights(stats: MoodStatistics?): List<String> {
        if (stats == null || stats.totalEntries == 0) {
            return listOf("开始记录心情来获得个性化洞察！")
        }
        
        val insights = mutableListOf<String>()
        
        // 平均心情洞察
        when {
            stats.averageMood >= 3.5 -> insights.add("你的整体心情很棒！保持积极的生活态度。")
            stats.averageMood >= 2.5 -> insights.add("你的心情总体还不错，继续保持平衡的生活。")
            stats.averageMood >= 1.5 -> insights.add("最近心情有些起伏，试着找到让自己开心的事情。")
            else -> insights.add("注意关注自己的心理健康，必要时寻求帮助。")
        }
        
        // 连续打卡洞察
        when {
            stats.currentStreak >= 30 -> insights.add("连续打卡${stats.currentStreak}天！你的坚持令人钦佩。")
            stats.currentStreak >= 7 -> insights.add("连续打卡${stats.currentStreak}天，养成了很好的习惯！")
            stats.currentStreak >= 3 -> insights.add("连续打卡${stats.currentStreak}天，继续保持！")
        }
        
        // 心情分布洞察
        val dominantMood = stats.moodDistribution.maxByOrNull { it.value }
        dominantMood?.let { (mood, count) ->
            val percentage = (count.toFloat() / stats.totalEntries * 100).toInt()
            if (percentage > 40) {
                insights.add("你${percentage}%的时间都是${mood.description}的状态。")
            }
        }
        
        // 周模式洞察
        val bestDay = stats.weeklyAverage.maxByOrNull { it.value }?.key
        val worstDay = stats.weeklyAverage.minByOrNull { it.value }?.key
        
        if (bestDay != null && worstDay != null && bestDay != worstDay) {
            insights.add("${getDayName(bestDay)}通常是你心情最好的一天，${getDayName(worstDay)}心情相对较低。")
        }
        
        return insights.take(3) // 最多显示3条洞察
    }
    
    private fun getDayName(dayOfWeek: java.time.DayOfWeek): String {
        return when (dayOfWeek) {
            java.time.DayOfWeek.MONDAY -> "周一"
            java.time.DayOfWeek.TUESDAY -> "周二"
            java.time.DayOfWeek.WEDNESDAY -> "周三"
            java.time.DayOfWeek.THURSDAY -> "周四"
            java.time.DayOfWeek.FRIDAY -> "周五"
            java.time.DayOfWeek.SATURDAY -> "周六"
            java.time.DayOfWeek.SUNDAY -> "周日"
        }
    }
}
