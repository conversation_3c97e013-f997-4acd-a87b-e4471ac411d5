package com.syferie.moodtracker.ui.screens.dailyentry

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.theme.MoodColors

@Composable
fun MoodSaveModal(
    selectedMood: MoodLevel,
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onSave: (String?) -> Unit,
    isLoading: Boolean = false
) {
    if (isVisible) {
        var noteText by remember { mutableStateOf("") }
        val context = LocalContext.current
        
        // 获取心情图标资源ID
        val imageRes = remember(selectedMood) {
            context.resources.getIdentifier(
                "mood_${selectedMood.iconRes}",
                "drawable",
                context.packageName
            )
        }
        
        val scale by animateFloatAsState(
            targetValue = if (isVisible) 1.0f else 0.8f,
            animationSpec = tween(300),
            label = "modalScale"
        )
        
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .scale(scale)
                    .padding(16.dp),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 心情图标显示
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .clip(RoundedCornerShape(20.dp))
                            .background(MoodColors.getMoodColor(selectedMood).copy(alpha = 0.1f)),
                        contentAlignment = Alignment.Center
                    ) {
                        if (imageRes != 0) {
                            Image(
                                painter = painterResource(id = imageRes),
                                contentDescription = selectedMood.description,
                                modifier = Modifier.size(64.dp),
                                contentScale = ContentScale.Fit
                            )
                        } else {
                            Text(
                                text = selectedMood.emoji,
                                fontSize = 48.sp
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 心情描述
                    Text(
                        text = selectedMood.description,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MoodColors.getMoodColor(selectedMood)
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 备注输入框
                    OutlinedTextField(
                        value = noteText,
                        onValueChange = { if (it.length <= 140) noteText = it },
                        label = { Text("添加备注 (可选)") },
                        placeholder = { Text("记录今天发生的事情...") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3,
                        supportingText = {
                            Text(
                                text = "${noteText.length}/140",
                                textAlign = TextAlign.End,
                                modifier = Modifier.fillMaxWidth()
                            )
                        },
                        shape = RoundedCornerShape(12.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text("取消")
                        }
                        
                        // 保存按钮
                        Button(
                            onClick = {
                                onSave(noteText.takeIf { it.isNotBlank() })
                            },
                            enabled = !isLoading,
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(12.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MoodColors.getMoodColor(selectedMood)
                            )
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    color = MaterialTheme.colorScheme.onPrimary,
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text(
                                    text = "保存",
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
