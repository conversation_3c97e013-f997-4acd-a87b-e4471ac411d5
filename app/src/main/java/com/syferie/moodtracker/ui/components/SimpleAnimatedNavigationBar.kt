package com.syferie.moodtracker.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.syferie.moodtracker.ui.navigation.MoodTrackerDestination
import com.syferie.moodtracker.ui.utils.PerformanceMonitor

/**
 * 简单但流畅的动画导航栏组件，确保布局稳定
 */
@Composable
fun SimpleAnimatedNavigationBar(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination?.route
    
    NavigationBar(
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface
    ) {
        MoodTrackerDestination.values().forEach { destination ->
            val isSelected = currentDestination == destination.route
            
            // 动画化图标缩放
            val iconScale by animateFloatAsState(
                targetValue = if (isSelected) 1.15f else 1.0f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessHigh
                ),
                label = "iconScale"
            )
            
            // 动画化颜色过渡
            val iconColor by animateColorAsState(
                targetValue = if (isSelected) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant,
                animationSpec = tween(300),
                label = "iconColor"
            )
            
            val textColor by animateColorAsState(
                targetValue = if (isSelected) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant,
                animationSpec = tween(300),
                label = "textColor"
            )
            
            // 使用key来优化重组性能
            key(destination.route) {
                NavigationBarItem(
                    icon = {
                        Box(
                            modifier = Modifier.scale(iconScale),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = destination.icon,
                                contentDescription = destination.title,
                                tint = iconColor
                            )
                        }
                    },
                    label = { 
                        Text(
                            text = destination.title,
                            color = textColor
                        ) 
                    },
                    selected = isSelected,
                    onClick = {
                        // 避免重复导航到同一页面
                        if (!isSelected) {
                            PerformanceMonitor.logNavigationTime(
                                from = currentDestination ?: "unknown",
                                to = destination.route
                            )
                            navController.navigate(destination.route) {
                                popUpTo(navController.graph.startDestinationId) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    },
                    colors = NavigationBarItemDefaults.colors(
                        selectedIconColor = MaterialTheme.colorScheme.primary,
                        selectedTextColor = MaterialTheme.colorScheme.primary,
                        unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        indicatorColor = MaterialTheme.colorScheme.primaryContainer.copy(0.4f)
                    )
                )
            }
        }
    }
}
