package com.syferie.moodtracker.ui.screens.stats

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.syferie.moodtracker.ui.components.MoodDistributionChart
import com.syferie.moodtracker.ui.components.MoodTrendChart
import com.syferie.moodtracker.ui.viewmodel.StatisticsViewModel
import com.syferie.moodtracker.ui.viewmodel.StatsPeriod
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedStatsScreen(
    statisticsViewModel: StatisticsViewModel = hiltViewModel()
) {
    val uiState by statisticsViewModel.uiState.collectAsStateWithLifecycle()
    val insights by statisticsViewModel.getMoodInsights().collectAsStateWithLifecycle(initialValue = emptyList())
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "心情统计",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        // 时间段选择器
        item {
            PeriodSelector(
                selectedPeriod = uiState.selectedPeriod,
                onPeriodSelected = statisticsViewModel::selectPeriod
            )
        }
        
        // 总体统计卡片
        item {
            uiState.statistics?.let { stats ->
                OverallStatsCard(stats = stats)
            }
        }
        
        // 心情分布图表
        item {
            uiState.statistics?.let { stats ->
                MoodDistributionChart(moodCounts = stats.moodDistribution)
            }
        }
        
        // 心情趋势图表
        item {
            when (uiState.selectedPeriod) {
                StatsPeriod.WEEK -> {
                    val weeklyTrend = uiState.weeklyStats.map {
                        it.weekStart.format(DateTimeFormatter.ofPattern("MM/dd")) to it.averageMood.toInt()
                    }
                    MoodTrendChart(moodHistory = weeklyTrend)
                }
                StatsPeriod.MONTH -> {
                    val monthlyTrend = uiState.monthlyStats.map {
                        it.month to it.averageMood.toInt()
                    }
                    MoodTrendChart(moodHistory = monthlyTrend)
                }
                else -> {
                    uiState.statistics?.let { stats ->
                        val trendData = stats.monthlyTrend.map { (month, avg) ->
                            month to avg.toInt()
                        }
                        MoodTrendChart(moodHistory = trendData)
                    }
                }
            }
        }
        
        // 洞察卡片
        item {
            InsightsCard(insights = insights)
        }
        
        // 详细统计
        when (uiState.selectedPeriod) {
            StatsPeriod.WEEK -> {
                items(uiState.weeklyStats) { weekStats ->
                    WeeklyStatsCard(stats = weekStats)
                }
            }
            StatsPeriod.MONTH -> {
                items(uiState.monthlyStats) { monthStats ->
                    MonthlyStatsCard(stats = monthStats)
                }
            }
            else -> {
                // 显示其他统计信息
            }
        }
    }
}

@Composable
private fun PeriodSelector(
    selectedPeriod: StatsPeriod,
    onPeriodSelected: (StatsPeriod) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间范围",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                StatsPeriod.values().forEach { period ->
                    FilterChip(
                        onClick = { onPeriodSelected(period) },
                        label = { 
                            Text(
                                text = when (period) {
                                    StatsPeriod.WEEK -> "周"
                                    StatsPeriod.MONTH -> "月"
                                    StatsPeriod.YEAR -> "年"
                                    StatsPeriod.ALL_TIME -> "全部"
                                }
                            )
                        },
                        selected = selectedPeriod == period
                    )
                }
            }
        }
    }
}

@Composable
private fun OverallStatsCard(
    stats: com.syferie.moodtracker.data.repository.MoodStatistics
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "总体统计",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "总记录",
                    value = stats.totalEntries.toString()
                )
                
                StatItem(
                    label = "平均心情",
                    value = String.format("%.1f", stats.averageMood)
                )
                
                StatItem(
                    label = "当前连续",
                    value = "${stats.currentStreak}天"
                )
                
                StatItem(
                    label = "最长连续",
                    value = "${stats.longestStreak}天"
                )
            }
        }
    }
}

@Composable
private fun InsightsCard(
    insights: List<String>
) {
    if (insights.isEmpty()) return
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "个性化洞察",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            insights.forEach { insight ->
                Text(
                    text = "• $insight",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
private fun WeeklyStatsCard(
    stats: com.syferie.moodtracker.data.repository.WeeklyStats
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "${stats.weekStart.format(DateTimeFormatter.ofPattern("MM/dd"))} - ${stats.weekEnd.format(DateTimeFormatter.ofPattern("MM/dd"))}",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "平均: ${String.format("%.1f", stats.averageMood)}",
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "记录: ${stats.totalEntries}天",
                    style = MaterialTheme.typography.bodySmall
                )
                stats.dominantMood?.let { mood ->
                    Text(
                        text = "主要: ${mood.emoji}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

@Composable
private fun MonthlyStatsCard(
    stats: com.syferie.moodtracker.data.repository.MonthlyStats
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "${stats.month} ${stats.year}",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "平均: ${String.format("%.1f", stats.averageMood)}",
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "记录: ${stats.totalEntries}天",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
