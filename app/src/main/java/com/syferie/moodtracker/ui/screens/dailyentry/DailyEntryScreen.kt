package com.syferie.moodtracker.ui.screens.dailyentry

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.animation.AnimationConstants
import com.syferie.moodtracker.ui.animation.FadeInAnimation
import com.syferie.moodtracker.ui.animation.SlideInAnimation
import com.syferie.moodtracker.ui.animation.pressAnimation
import com.syferie.moodtracker.ui.components.LoadingIndicator
import com.syferie.moodtracker.ui.components.SuccessFeedback
import com.syferie.moodtracker.ui.theme.MoodColors
import com.syferie.moodtracker.ui.viewmodel.MoodViewModel
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DailyEntryScreen(
    moodViewModel: MoodViewModel
) {
    val selectedDate by moodViewModel.selectedDate.collectAsStateWithLifecycle()
    val currentMood by moodViewModel.currentMood.collectAsStateWithLifecycle()
    val isLoading by moodViewModel.isLoading.collectAsStateWithLifecycle()
    val errorMessage by moodViewModel.errorMessage.collectAsStateWithLifecycle()

    var selectedMoodLevel by remember { mutableStateOf<MoodLevel?>(null) }
    var showMoodSaveModal by remember { mutableStateOf(false) }
    var showSuccessFeedback by remember { mutableStateOf(false) }
    var successMessage by remember { mutableStateOf("") }

    // 当选中日期或当前心情变化时，更新UI状态
    LaunchedEffect(selectedDate, currentMood) {
        selectedMoodLevel = currentMood?.let { mood ->
            MoodLevel.fromValue(mood.mood)
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
        // 日期显示
        SlideInAnimation(visible = true) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = selectedMoodLevel?.let {
                        MoodColors.getMoodColor(it).copy(alpha = 0.1f)
                    } ?: MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日")),
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (selectedDate.dayOfWeek.value) {
                            1 -> "星期一"
                            2 -> "星期二"
                            3 -> "星期三"
                            4 -> "星期四"
                            5 -> "星期五"
                            6 -> "星期六"
                            7 -> "星期日"
                            else -> ""
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 心情选择标题
        FadeInAnimation(visible = true) {
            Text(
                text = if (currentMood != null) "修改今日心情" else "记录今日心情",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Medium
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 心情选择按钮
        MoodSelectionRow(
            selectedMood = selectedMoodLevel,
            onMoodSelected = { mood ->
                selectedMoodLevel = mood
                showMoodSaveModal = true
            },
            modifier = Modifier.weight(1f)
        )

        // 删除按钮（仅在有记录时显示）
        if (currentMood != null) {
            Spacer(modifier = Modifier.height(16.dp))
            OutlinedButton(
                onClick = { moodViewModel.deleteMood() },
                enabled = !isLoading,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp)
            ) {
                Text("删除记录")
            }
        }
        
            // 错误信息显示
            errorMessage?.let { error ->
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = error,
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }

        // Modal弹窗
        selectedMoodLevel?.let { mood ->
            MoodSaveModal(
                selectedMood = mood,
                isVisible = showMoodSaveModal,
                onDismiss = {
                    showMoodSaveModal = false
                    // 重置为当前已保存的心情状态，如果没有保存的心情则重置为null
                    selectedMoodLevel = currentMood?.let { MoodLevel.fromValue(it.mood) }
                },
                onSave = { note ->
                    if (currentMood != null) {
                        moodViewModel.updateMood(mood, note)
                        successMessage = "心情记录已更新！"
                    } else {
                        moodViewModel.saveMood(mood, note)
                        successMessage = "心情记录已保存！"
                    }
                    showMoodSaveModal = false
                    showSuccessFeedback = true
                },
                isLoading = isLoading
            )
        }

        // 加载指示器
        LoadingIndicator(visible = isLoading)

        // 成功反馈
        SuccessFeedback(
            visible = showSuccessFeedback,
            message = successMessage,
            onDismiss = { showSuccessFeedback = false },
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}
