package com.syferie.moodtracker.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.syferie.moodtracker.ui.navigation.MoodTrackerDestination
import com.syferie.moodtracker.ui.utils.PerformanceMonitor

/**
 * 高级动画导航栏组件，带有流畅的选中动画和移动指示器效果
 */
@Composable
fun AnimatedNavigationBar(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination?.route

    // 获取当前选中项的索引
    val selectedIndex = remember(currentDestination) {
        MoodTrackerDestination.values().indexOfFirst { it.route == currentDestination }
            .takeIf { it >= 0 } ?: 0
    }

    // 动画化选中指示器的位置
    val animatedSelectedIndex by animateFloatAsState(
        targetValue = selectedIndex.toFloat(),
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "selectedIndex"
    )

    // 使用Box来叠加自定义指示器，但限制在NavigationBar的范围内
    Box(modifier = modifier) {
        NavigationBar(
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface
        ) {
            MoodTrackerDestination.values().forEachIndexed { index, destination ->
                val isSelected = currentDestination == destination.route

                // 动画化图标缩放
                val iconScale by animateFloatAsState(
                    targetValue = if (isSelected) 1.2f else 1.0f,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessHigh
                    ),
                    label = "iconScale"
                )

                // 使用key来优化重组性能
                key(destination.route) {
                    NavigationBarItem(
                        icon = {
                            Box(
                                modifier = Modifier.scale(iconScale),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = destination.icon,
                                    contentDescription = destination.title,
                                    tint = if (isSelected)
                                        MaterialTheme.colorScheme.primary
                                    else
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        },
                        label = {
                            Text(
                                text = destination.title,
                                color = if (isSelected)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        },
                        selected = isSelected,
                        onClick = {
                            // 避免重复导航到同一页面
                            if (!isSelected) {
                                PerformanceMonitor.logNavigationTime(
                                    from = currentDestination ?: "unknown",
                                    to = destination.route
                                )
                                navController.navigate(destination.route) {
                                    popUpTo(navController.graph.startDestinationId) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        },
                        colors = NavigationBarItemDefaults.colors(
                            selectedIconColor = MaterialTheme.colorScheme.primary,
                            selectedTextColor = MaterialTheme.colorScheme.primary,
                            unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                            unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                            indicatorColor = Color.Transparent // 使用自定义指示器
                        )
                    )
                }
            }
        }

        // 流畅移动的选中指示器 - 正确定位在NavigationBar内部
        MovingSelectionIndicator(
            selectedIndex = animatedSelectedIndex,
            itemCount = MoodTrackerDestination.values().size,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp) // 限制在NavigationBar的高度内
                .align(Alignment.BottomCenter)
        )
    }
}

/**
 * 流畅移动的选中指示器，正确定位在NavigationBar底部
 */
@Composable
private fun MovingSelectionIndicator(
    selectedIndex: Float,
    itemCount: Int,
    modifier: Modifier = Modifier
) {
    // 波浪动画
    val infiniteTransition = rememberInfiniteTransition(label = "wave")
    val waveOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "waveOffset"
    )

    BoxWithConstraints(
        modifier = modifier
    ) {
        val itemWidth = maxWidth / itemCount
        val indicatorWidth = 36.dp // 进一步优化宽度，确保居中
        val indicatorHeight = 3.dp

        // 精确计算指示器的X位置，确保完全居中对齐到导航项
        val itemCenterX = itemWidth * selectedIndex + itemWidth / 2
        val indicatorX = itemCenterX - indicatorWidth / 2

        // 动画化指示器的颜色
        val indicatorColor by animateColorAsState(
            targetValue = MaterialTheme.colorScheme.primary,
            animationSpec = tween(300),
            label = "indicatorColor"
        )

        // 背景光晕效果 - 精确定位
        Box(
            modifier = Modifier
                .offset(x = indicatorX - 4.dp, y = maxHeight - 24.dp) // 进一步向上移动，光晕稍微扩展
                .size(indicatorWidth + 8.dp, indicatorHeight + 4.dp)
                .clip(RoundedCornerShape(50))
                .background(
                    indicatorColor.copy(0.12f + 0.08f * kotlin.math.sin(waveOffset * 2 * kotlin.math.PI).toFloat()),
                    RoundedCornerShape(50)
                )
        )

        // 主指示器 - 精确居中定位
        Box(
            modifier = Modifier
                .offset(x = indicatorX, y = maxHeight - 22.dp) // 进一步向上移动
                .size(indicatorWidth, indicatorHeight)
                .clip(RoundedCornerShape(50))
                .background(indicatorColor, RoundedCornerShape(50))
        )

        // 流动的小点效果 - 精确定位
        Box(
            modifier = Modifier
                .offset(
                    x = indicatorX + indicatorWidth * (0.25f + 0.5f * waveOffset), // 调整流动范围
                    y = maxHeight - 21.5.dp // 进一步向上移动
                )
                .size(1.5.dp)
                .clip(CircleShape)
                .background(
                    indicatorColor.copy(0.85f),
                    CircleShape
                )
        )
    }
}


