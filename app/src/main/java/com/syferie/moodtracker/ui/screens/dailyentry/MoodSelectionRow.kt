package com.syferie.moodtracker.ui.screens.dailyentry

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.animation.AnimationConstants
import com.syferie.moodtracker.ui.animation.BounceAnimation
import com.syferie.moodtracker.ui.animation.moodButtonAnimation
import com.syferie.moodtracker.ui.theme.MoodColors

@Composable
fun MoodSelectionRow(
    selectedMood: MoodLevel?,
    onMoodSelected: (MoodLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    // 使用网格布局显示12个心情图标，每行4个
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(vertical = 16.dp)
    ) {
        items(MoodLevel.values().toList()) { mood ->
            MoodButton(
                mood = mood,
                isSelected = selectedMood == mood,
                onClick = { onMoodSelected(mood) }
            )
        }
    }
}

@Composable
private fun MoodButton(
    mood: MoodLevel,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "moodButtonScale"
    )

    // 获取图片资源ID
    val imageRes = remember(mood) {
        context.resources.getIdentifier(
            "mood_${mood.iconRes}",
            "drawable",
            context.packageName
        )
    }

    Box(
        modifier = modifier
            .size(72.dp)
            .scale(scale)
            .clip(RoundedCornerShape(16.dp))
            .background(
                if (isSelected) {
                    MoodColors.getMoodColor(mood).copy(alpha = 0.2f)
                } else {
                    Color.Transparent
                }
            )
            .clickable { onClick() }
            .moodButtonAnimation(isSelected, onClick),
        contentAlignment = Alignment.Center
    ) {
        if (imageRes != 0) {
            Image(
                painter = painterResource(id = imageRes),
                contentDescription = mood.description,
                modifier = Modifier.size(56.dp),
                contentScale = ContentScale.Fit
            )
        } else {
            // 备用方案：如果图片加载失败，显示emoji
            Text(
                text = mood.emoji,
                fontSize = 32.sp
            )
        }
    }
}
