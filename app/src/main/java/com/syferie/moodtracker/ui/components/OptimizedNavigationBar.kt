package com.syferie.moodtracker.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.syferie.moodtracker.ui.navigation.MoodTrackerDestination
import com.syferie.moodtracker.ui.utils.PerformanceMonitor

/**
 * 优化的导航栏组件，带有流畅的选中动画效果
 */
@Composable
fun OptimizedNavigationBar(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination?.route

    // 获取当前选中项的索引
    val selectedIndex = remember(currentDestination) {
        MoodTrackerDestination.values().indexOfFirst { it.route == currentDestination }
            .takeIf { it >= 0 } ?: 0
    }

    // 动画化选中指示器的位置
    val animatedSelectedIndex by animateFloatAsState(
        targetValue = selectedIndex.toFloat(),
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "selectedIndex"
    )

    Box(modifier = modifier) {
        NavigationBar {
            MoodTrackerDestination.values().forEachIndexed { index, destination ->
                val isSelected = currentDestination == destination.route

                // 使用key来优化重组性能
                key(destination.route) {
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = destination.icon,
                                contentDescription = destination.title
                            )
                        },
                        label = { Text(destination.title) },
                        selected = isSelected,
                        onClick = {
                            // 避免重复导航到同一页面
                            if (!isSelected) {
                                PerformanceMonitor.logNavigationTime(
                                    from = currentDestination ?: "unknown",
                                    to = destination.route
                                )
                                navController.navigate(destination.route) {
                                    popUpTo(navController.graph.startDestinationId) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        }
                    )
                }
            }
        }

        // 流畅移动的选中指示器
        AnimatedSelectionIndicator(
            selectedIndex = animatedSelectedIndex,
            itemCount = MoodTrackerDestination.values().size
        )
    }
}

/**
 * 流畅移动的选中指示器
 */
@Composable
private fun AnimatedSelectionIndicator(
    selectedIndex: Float,
    itemCount: Int,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current

    // 计算指示器的位置和大小
    BoxWithConstraints(
        modifier = modifier.fillMaxSize()
    ) {
        val itemWidth = maxWidth / itemCount
        val indicatorWidth = 40.dp
        val indicatorHeight = 4.dp

        // 计算指示器的X位置
        val indicatorX = itemWidth * selectedIndex + (itemWidth - indicatorWidth) / 2

        // 动画化指示器的颜色
        val indicatorColor by animateColorAsState(
            targetValue = MaterialTheme.colorScheme.primary,
            animationSpec = tween(300),
            label = "indicatorColor"
        )

        // 指示器背景圆形
        Box(
            modifier = Modifier
                .offset(x = indicatorX, y = 8.dp)
                .size(indicatorWidth, indicatorHeight + 8.dp)
                .clip(CircleShape)
                .background(
                    indicatorColor.copy(0.2f),
                    CircleShape
                )
        )

        // 主指示器
        Box(
            modifier = Modifier
                .offset(x = indicatorX, y = 12.dp)
                .size(indicatorWidth, indicatorHeight)
                .clip(CircleShape)
                .background(indicatorColor, CircleShape)
        )
    }
}
