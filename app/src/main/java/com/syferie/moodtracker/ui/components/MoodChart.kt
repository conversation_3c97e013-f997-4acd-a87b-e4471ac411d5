package com.syferie.moodtracker.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.syferie.moodtracker.data.model.MoodLevel
import com.syferie.moodtracker.ui.theme.MoodColors
import kotlin.math.max

@Composable
fun MoodDistributionChart(
    moodCounts: Map<MoodLevel, Int>,
    modifier: Modifier = Modifier
) {
    val totalCount = moodCounts.values.sum()
    if (totalCount == 0) {
        EmptyChartPlaceholder(modifier = modifier)
        return
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "心情分布",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 水平条形图
            MoodLevel.values().forEach { mood ->
                val count = moodCounts[mood] ?: 0
                val percentage = if (totalCount > 0) count.toFloat() / totalCount else 0f
                
                MoodBarItem(
                    mood = mood,
                    count = count,
                    percentage = percentage,
                    maxCount = moodCounts.values.maxOrNull() ?: 1
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun MoodBarItem(
    mood: MoodLevel,
    count: Int,
    percentage: Float,
    maxCount: Int
) {
    var animationPlayed by remember { mutableStateOf(false) }
    
    val animatedWidth by animateFloatAsState(
        targetValue = if (animationPlayed) count.toFloat() / maxCount else 0f,
        animationSpec = tween(
            durationMillis = 1000,
            delayMillis = mood.value * 100,
            easing = FastOutSlowInEasing
        ),
        label = "barWidth"
    )
    
    LaunchedEffect(Unit) {
        animationPlayed = true
    }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 心情表情
        Text(
            text = mood.emoji,
            fontSize = 20.sp,
            modifier = Modifier.width(32.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 心情描述
        Text(
            text = mood.description,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(60.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 进度条
        Box(
            modifier = Modifier
                .weight(1f)
                .height(20.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(animatedWidth)
                    .background(
                        MoodColors.getMoodColor(mood),
                        RoundedCornerShape(10.dp)
                    )
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 数量和百分比
        Text(
            text = "$count (${(percentage * 100).toInt()}%)",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.width(80.dp)
        )
    }
}

@Composable
fun MoodTrendChart(
    moodHistory: List<Pair<String, Int>>, // 日期和心情值的对
    modifier: Modifier = Modifier
) {
    if (moodHistory.isEmpty()) {
        EmptyChartPlaceholder(modifier = modifier)
        return
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "心情趋势",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 简单的折线图
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
            ) {
                drawMoodTrendLine(moodHistory)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 图例
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                if (moodHistory.isNotEmpty()) {
                    Text(
                        text = moodHistory.first().first,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = moodHistory.last().first,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

private fun DrawScope.drawMoodTrendLine(moodHistory: List<Pair<String, Int>>) {
    if (moodHistory.size < 2) return
    
    val width = size.width
    val height = size.height
    val padding = 40f
    
    val maxMood = 4f
    val minMood = 0f
    
    val stepX = (width - 2 * padding) / (moodHistory.size - 1)
    
    val path = Path()
    val points = mutableListOf<Offset>()
    
    moodHistory.forEachIndexed { index, (_, mood) ->
        val x = padding + index * stepX
        val y = height - padding - ((mood - minMood) / (maxMood - minMood)) * (height - 2 * padding)
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // 绘制折线
    drawPath(
        path = path,
        color = Color(0xFF4CAF50),
        style = Stroke(width = 4.dp.toPx())
    )
    
    // 绘制数据点
    points.forEachIndexed { index, point ->
        val mood = moodHistory[index].second
        val color = MoodColors.getMoodColorByValue(mood)
        
        drawCircle(
            color = color,
            radius = 8.dp.toPx(),
            center = point
        )
        
        drawCircle(
            color = Color.White,
            radius = 4.dp.toPx(),
            center = point
        )
    }
}

@Composable
private fun EmptyChartPlaceholder(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "📊",
                    fontSize = 48.sp
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "暂无数据",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "开始记录心情来查看统计",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
