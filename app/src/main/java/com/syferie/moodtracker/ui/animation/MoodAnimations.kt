package com.syferie.moodtracker.ui.animation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

// 动画常量
object AnimationConstants {
    const val FAST_ANIMATION = 150
    const val NORMAL_ANIMATION = 300
    const val SLOW_ANIMATION = 500
    
    // 缓动曲线 t = (1 - cos(π * x)) / 2
    val EaseInOutSine = CubicBezierEasing(0.37f, 0f, 0.63f, 1f)
    val BounceEasing = CubicBezierEasing(0.68f, -0.55f, 0.265f, 1.55f)
}

// 按钮按下动画
fun Modifier.pressAnimation(
    pressScale: Float = 0.95f,
    animationDuration: Int = AnimationConstants.FAST_ANIMATION
): Modifier = composed {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) pressScale else 1f,
        animationSpec = tween(
            durationMillis = animationDuration,
            easing = AnimationConstants.EaseInOutSine
        ),
        label = "pressScale"
    )
    
    this
        .scale(scale)
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            onClick = { }
        )
}

// 心情按钮特殊动画
fun Modifier.moodButtonAnimation(
    isSelected: Boolean,
    onClick: () -> Unit
): Modifier = composed {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = when {
            isPressed -> 0.9f
            isSelected -> 1.1f
            else -> 1f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "moodButtonScale"
    )
    
    val elevation by animateFloatAsState(
        targetValue = if (isSelected) 8f else 2f,
        animationSpec = tween(
            durationMillis = AnimationConstants.NORMAL_ANIMATION,
            easing = AnimationConstants.EaseInOutSine
        ),
        label = "moodButtonElevation"
    )
    
    this
        .scale(scale)
        .graphicsLayer {
            shadowElevation = elevation
        }
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            onClick = onClick
        )
}

// 淡入淡出动画
@Composable
fun FadeInAnimation(
    visible: Boolean,
    duration: Int = AnimationConstants.NORMAL_ANIMATION,
    content: @Composable () -> Unit
) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        ),
        exit = fadeOut(
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        )
    ) {
        content()
    }
}

// 滑入动画
@Composable
fun SlideInAnimation(
    visible: Boolean,
    slideDistance: Dp = 50.dp,
    duration: Int = AnimationConstants.NORMAL_ANIMATION,
    content: @Composable () -> Unit
) {
    val density = LocalDensity.current
    val slideDistancePx = with(density) { slideDistance.roundToPx() }
    
    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(
            initialOffsetY = { slideDistancePx },
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        ),
        exit = slideOutVertically(
            targetOffsetY = { slideDistancePx },
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = duration,
                easing = AnimationConstants.EaseInOutSine
            )
        )
    ) {
        content()
    }
}

// 弹跳动画
@Composable
fun BounceAnimation(
    trigger: Boolean,
    content: @Composable () -> Unit
) {
    var animationPlayed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (trigger && !animationPlayed) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        finishedListener = {
            if (trigger) animationPlayed = true
        },
        label = "bounceScale"
    )
    
    LaunchedEffect(trigger) {
        if (!trigger) animationPlayed = false
    }
    
    Box(
        modifier = Modifier.scale(scale)
    ) {
        content()
    }
}
