package com.syferie.moodtracker.ui.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.syferie.moodtracker.ui.screens.calendar.CalendarScreen
import com.syferie.moodtracker.ui.screens.dailyentry.DailyEntryScreen
import com.syferie.moodtracker.ui.screens.settings.SettingsScreen
import com.syferie.moodtracker.ui.screens.stats.StatsScreen
import com.syferie.moodtracker.ui.viewmodel.MoodViewModel

@Composable
fun MoodTrackerNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    moodViewModel: MoodViewModel = hiltViewModel()
) {
    NavHost(
        navController = navController,
        startDestination = MoodTrackerDestination.DAILY_ENTRY.route,
        modifier = modifier,
        enterTransition = {
            // 使用更丝滑的动画组合：淡入 + 轻微缩放
            fadeIn(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing
                )
            ) + scaleIn(
                initialScale = 0.95f,
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing
                )
            )
        },
        exitTransition = {
            fadeOut(
                animationSpec = tween(
                    durationMillis = 150,
                    easing = FastOutLinearInEasing
                )
            ) + scaleOut(
                targetScale = 1.05f,
                animationSpec = tween(
                    durationMillis = 150,
                    easing = FastOutLinearInEasing
                )
            )
        },
        popEnterTransition = {
            fadeIn(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing
                )
            ) + scaleIn(
                initialScale = 0.95f,
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing
                )
            )
        },
        popExitTransition = {
            fadeOut(
                animationSpec = tween(
                    durationMillis = 150,
                    easing = FastOutLinearInEasing
                )
            ) + scaleOut(
                targetScale = 1.05f,
                animationSpec = tween(
                    durationMillis = 150,
                    easing = FastOutLinearInEasing
                )
            )
        }
    ) {
        composable(MoodTrackerDestination.DAILY_ENTRY.route) {
            DailyEntryScreen(moodViewModel = moodViewModel)
        }

        composable(MoodTrackerDestination.CALENDAR.route) {
            CalendarScreen(moodViewModel = moodViewModel)
        }

        composable(MoodTrackerDestination.STATS.route) {
            StatsScreen(moodViewModel = moodViewModel)
        }

        composable(MoodTrackerDestination.SETTINGS.route) {
            SettingsScreen()
        }
    }
}
