package com.syferie.moodtracker.ui.theme

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.syferie.moodtracker.data.model.MoodLevel

// 心情对应的颜色 - 扩展到12种
object MoodColors {
    // 愤怒系列 - 红色调
    val VeryAngry = Color(0xFFD32F2F)        // 深红色
    val Angry = Color(0xFFE57373)            // 浅红色

    // 悲伤系列 - 蓝色调
    val VerySad = Color(0xFF1976D2)          // 深蓝色
    val Sad = Color(0xFF64B5F6)              // 浅蓝色

    // 担心系列 - 紫色调
    val Worried = Color(0xFF7B1FA2)          // 紫色

    // 中性系列 - 灰色调
    val Neutral = Color(0xFF757575)          // 灰色
    val Calm = Color(0xFF90A4AE)             // 浅灰蓝

    // 开心系列 - 绿色调
    val Happy = Color(0xFF4CAF50)            // 绿色
    val VeryHappy = Color(0xFF66BB6A)        // 浅绿色

    // 兴奋系列 - 橙色调
    val Excited = Color(0xFFFF9800)          // 橙色

    // 喜爱系列 - 粉色调
    val Love = Color(0xFFE91E63)             // 粉红色
    val Blissful = Color(0xFFFF69B4)         // 亮粉色

    fun getMoodColor(moodLevel: MoodLevel, isDark: Boolean = false): Color {
        return when (moodLevel) {
            MoodLevel.VERY_ANGRY -> VeryAngry
            MoodLevel.ANGRY -> Angry
            MoodLevel.VERY_SAD -> VerySad
            MoodLevel.SAD -> Sad
            MoodLevel.WORRIED -> Worried
            MoodLevel.NEUTRAL -> Neutral
            MoodLevel.CALM -> Calm
            MoodLevel.HAPPY -> Happy
            MoodLevel.VERY_HAPPY -> VeryHappy
            MoodLevel.EXCITED -> Excited
            MoodLevel.LOVE -> Love
            MoodLevel.BLISSFUL -> Blissful
        }
    }
    
    fun getMoodColorByValue(value: Int, isDark: Boolean = false): Color {
        return getMoodColor(MoodLevel.fromValue(value), isDark)
    }
}

// 扩展ColorScheme以支持心情颜色
@Composable
fun ColorScheme.moodColor(moodLevel: MoodLevel): Color {
    return MoodColors.getMoodColor(moodLevel, false)
}

@Composable
fun ColorScheme.moodColorByValue(value: Int): Color {
    return MoodColors.getMoodColorByValue(value, false)
}
