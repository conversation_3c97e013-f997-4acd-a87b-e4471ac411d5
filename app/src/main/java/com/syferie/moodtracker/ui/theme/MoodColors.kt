package com.syferie.moodtracker.ui.theme

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.syferie.moodtracker.data.model.MoodLevel

// 心情对应的颜色 - 扩展到12种
object MoodColors {
    // 纠结愁容 - 灰紫色调
    val Confused = Color(0xFF9C27B0)         // 紫色

    // 微怒于心 - 橙红色调
    val SlightlyAngry = Color(0xFFFF5722)    // 橙红色

    // 甜蜜时光 - 粉色调
    val Sweet = Color(0xFFE91E63)            // 粉红色

    // 开心嗨皮 - 绿色调
    val Happy = Color(0xFF4CAF50)            // 绿色

    // 尴尬忧思 - 黄色调
    val Embarrassed = Color(0xFFFFC107)      // 黄色

    // 伤心难过 - 蓝色调
    val Sad = Color(0xFF2196F3)              // 蓝色

    // 挑战好奇 - 青色调
    val Curious = Color(0xFF00BCD4)          // 青色

    // 收获满满 - 深绿色调
    val Fulfilled = Color(0xFF388E3C)        // 深绿色

    // 耀眼自豪 - 金色调
    val Proud = Color(0xFFFF9800)            // 橙色

    // 哎呦烦人 - 棕色调
    val Annoyed = Color(0xFF795548)          // 棕色

    // muad害怕 - 深紫色调
    val Scared = Color(0xFF673AB7)           // 深紫色

    // 疲倦无力 - 灰色调
    val Tired = Color(0xFF607D8B)            // 蓝灰色

    fun getMoodColor(moodLevel: MoodLevel, isDark: Boolean = false): Color {
        return when (moodLevel) {
            MoodLevel.CONFUSED -> Confused
            MoodLevel.SLIGHTLY_ANGRY -> SlightlyAngry
            MoodLevel.SWEET -> Sweet
            MoodLevel.HAPPY -> Happy
            MoodLevel.EMBARRASSED -> Embarrassed
            MoodLevel.SAD -> Sad
            MoodLevel.CURIOUS -> Curious
            MoodLevel.FULFILLED -> Fulfilled
            MoodLevel.PROUD -> Proud
            MoodLevel.ANNOYED -> Annoyed
            MoodLevel.SCARED -> Scared
            MoodLevel.TIRED -> Tired
        }
    }
    
    fun getMoodColorByValue(value: Int, isDark: Boolean = false): Color {
        return getMoodColor(MoodLevel.fromValue(value), isDark)
    }
}

// 扩展ColorScheme以支持心情颜色
@Composable
fun ColorScheme.moodColor(moodLevel: MoodLevel): Color {
    return MoodColors.getMoodColor(moodLevel, false)
}

@Composable
fun ColorScheme.moodColorByValue(value: Int): Color {
    return MoodColors.getMoodColorByValue(value, false)
}
