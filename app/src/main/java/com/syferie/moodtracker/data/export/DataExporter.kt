package com.syferie.moodtracker.data.export

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import com.syferie.moodtracker.data.model.MoodEntity
import com.syferie.moodtracker.data.model.MoodLevel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.FileWriter
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

enum class ExportFormat {
    JSON, CSV
}

data class ExportResult(
    val success: Boolean,
    val filePath: String? = null,
    val error: String? = null
)

@Singleton
class DataExporter @Inject constructor() {
    
    suspend fun exportMoodData(
        context: Context,
        moods: List<MoodEntity>,
        format: ExportFormat
    ): ExportResult = withContext(Dispatchers.IO) {
        try {
            val fileName = "mood_data_${System.currentTimeMillis()}.${format.name.lowercase()}"
            val file = File(context.getExternalFilesDir(null), fileName)
            
            when (format) {
                ExportFormat.JSON -> exportToJson(moods, file)
                ExportFormat.CSV -> exportToCsv(moods, file)
            }
            
            ExportResult(success = true, filePath = file.absolutePath)
        } catch (e: Exception) {
            ExportResult(success = false, error = e.message)
        }
    }
    
    private fun exportToJson(moods: List<MoodEntity>, file: File) {
        val jsonArray = JSONArray()
        
        moods.forEach { mood ->
            val jsonObject = JSONObject().apply {
                put("date", mood.date.toString())
                put("mood", mood.mood)
                put("moodDescription", MoodLevel.fromValue(mood.mood).description)
                put("moodEmoji", MoodLevel.fromValue(mood.mood).emoji)
                put("note", mood.note ?: "")
            }
            jsonArray.put(jsonObject)
        }
        
        val exportData = JSONObject().apply {
            put("exportDate", System.currentTimeMillis())
            put("totalRecords", moods.size)
            put("data", jsonArray)
        }
        
        FileWriter(file).use { writer ->
            writer.write(exportData.toString(2))
        }
    }
    
    private fun exportToCsv(moods: List<MoodEntity>, file: File) {
        FileWriter(file).use { writer ->
            // CSV 头部
            writer.write("日期,心情值,心情描述,心情表情,备注\n")
            
            // 数据行
            moods.forEach { mood ->
                val moodLevel = MoodLevel.fromValue(mood.mood)
                writer.write(
                    "${mood.date}," +
                    "${mood.mood}," +
                    "\"${moodLevel.description}\"," +
                    "\"${moodLevel.emoji}\"," +
                    "\"${mood.note?.replace("\"", "\"\"") ?: ""}\"\n"
                )
            }
        }
    }
    
    suspend fun shareExportedFile(context: Context, filePath: String): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val file = File(filePath)
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
                
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = when {
                        filePath.endsWith(".json") -> "application/json"
                        filePath.endsWith(".csv") -> "text/csv"
                        else -> "text/plain"
                    }
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_SUBJECT, "心情记录数据")
                    putExtra(Intent.EXTRA_TEXT, "这是我的心情记录数据导出文件")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                val chooserIntent = Intent.createChooser(shareIntent, "分享心情数据")
                chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooserIntent)
                
                true
            } catch (e: Exception) {
                false
            }
        }
    }
    
    suspend fun generateStatisticsReport(
        context: Context,
        moods: List<MoodEntity>
    ): ExportResult = withContext(Dispatchers.IO) {
        try {
            val fileName = "mood_report_${System.currentTimeMillis()}.txt"
            val file = File(context.getExternalFilesDir(null), fileName)
            
            val report = generateReportContent(moods)
            
            FileWriter(file).use { writer ->
                writer.write(report)
            }
            
            ExportResult(success = true, filePath = file.absolutePath)
        } catch (e: Exception) {
            ExportResult(success = false, error = e.message)
        }
    }
    
    private fun generateReportContent(moods: List<MoodEntity>): String {
        if (moods.isEmpty()) {
            return "心情记录报告\n\n暂无数据"
        }
        
        val sortedMoods = moods.sortedBy { it.date }
        val totalDays = moods.size
        val averageMood = moods.map { it.mood }.average()
        val moodDistribution = MoodLevel.values().associateWith { level ->
            moods.count { it.mood == level.value }
        }
        
        val firstRecord = sortedMoods.first().date
        val lastRecord = sortedMoods.last().date
        
        return buildString {
            appendLine("心情记录报告")
            appendLine("===============================")
            appendLine()
            appendLine("报告生成时间: ${DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(java.time.LocalDateTime.now())}")
            appendLine("数据时间范围: $firstRecord 至 $lastRecord")
            appendLine()
            appendLine("总体统计:")
            appendLine("- 总记录天数: $totalDays 天")
            appendLine("- 平均心情: ${String.format("%.2f", averageMood)}")
            appendLine()
            appendLine("心情分布:")
            moodDistribution.forEach { (level, count) ->
                val percentage = (count.toFloat() / totalDays * 100).toInt()
                appendLine("- ${level.emoji} ${level.description}: $count 次 ($percentage%)")
            }
            appendLine()
            appendLine("详细记录:")
            sortedMoods.forEach { mood ->
                val level = MoodLevel.fromValue(mood.mood)
                appendLine("${mood.date}: ${level.emoji} ${level.description}" + 
                    if (mood.note?.isNotBlank() == true) " - ${mood.note}" else "")
            }
        }
    }
}
