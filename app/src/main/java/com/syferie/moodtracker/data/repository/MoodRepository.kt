package com.syferie.moodtracker.data.repository

import com.syferie.moodtracker.data.database.MoodDao
import com.syferie.moodtracker.data.model.MoodEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MoodRepository @Inject constructor(
    private val moodDao: MoodDao
) {
    
    fun getAllMoods(): Flow<List<MoodEntity>> = moodDao.getAllMoods()
    
    suspend fun getMoodByDate(date: LocalDate): MoodEntity? = withContext(Dispatchers.IO) {
        moodDao.getMoodByDate(date)
    }
    
    fun getMoodsBetweenDates(startDate: LocalDate, endDate: LocalDate): Flow<List<MoodEntity>> =
        moodDao.getMoodsBetweenDates(startDate, endDate)
    
    fun getMoodsFromDate(startDate: LocalDate): Flow<List<MoodEntity>> =
        moodDao.getMoodsFromDate(startDate)
    
    suspend fun insertMood(mood: MoodEntity) = withContext(Dispatchers.IO) {
        moodDao.insertMood(mood)
    }
    
    suspend fun updateMood(mood: MoodEntity) = withContext(Dispatchers.IO) {
        moodDao.updateMood(mood)
    }
    
    suspend fun deleteMood(mood: MoodEntity) = withContext(Dispatchers.IO) {
        moodDao.deleteMood(mood)
    }
    
    suspend fun deleteMoodByDate(date: LocalDate) = withContext(Dispatchers.IO) {
        moodDao.deleteMoodByDate(date)
    }
    
    suspend fun getMoodCount(): Int = withContext(Dispatchers.IO) {
        moodDao.getMoodCount()
    }
    
    suspend fun getAverageMoodBetweenDates(startDate: LocalDate, endDate: LocalDate): Double? = 
        withContext(Dispatchers.IO) {
            moodDao.getAverageMoodBetweenDates(startDate, endDate)
        }
}
