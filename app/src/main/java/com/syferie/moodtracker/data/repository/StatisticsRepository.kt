package com.syferie.moodtracker.data.repository

import com.syferie.moodtracker.data.model.MoodEntity
import com.syferie.moodtracker.data.model.MoodLevel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton

data class MoodStatistics(
    val totalEntries: Int,
    val averageMood: Double,
    val currentStreak: Int,
    val longestStreak: Int,
    val moodDistribution: Map<MoodLevel, Int>,
    val weeklyAverage: Map<DayOfWeek, Double>,
    val monthlyTrend: List<Pair<String, Double>>,
    val moodFrequency: Map<MoodLevel, Double>
)

data class WeeklyStats(
    val weekStart: LocalDate,
    val weekEnd: LocalDate,
    val averageMood: Double,
    val totalEntries: Int,
    val dominantMood: MoodLevel?
)

data class MonthlyStats(
    val month: String,
    val year: Int,
    val averageMood: Double,
    val totalEntries: Int,
    val bestDay: LocalDate?,
    val worstDay: LocalDate?
)

@Singleton
class StatisticsRepository @Inject constructor(
    private val moodRepository: MoodRepository
) {
    
    fun getMoodStatistics(): Flow<MoodStatistics> {
        return moodRepository.getAllMoods().map { moods ->
            calculateStatistics(moods)
        }
    }
    
    fun getWeeklyStats(startDate: LocalDate): Flow<List<WeeklyStats>> {
        return moodRepository.getAllMoods().map { moods ->
            calculateWeeklyStats(moods, startDate)
        }
    }
    
    fun getMonthlyStats(year: Int): Flow<List<MonthlyStats>> {
        return moodRepository.getAllMoods().map { moods ->
            calculateMonthlyStats(moods, year)
        }
    }
    
    private fun calculateStatistics(moods: List<MoodEntity>): MoodStatistics {
        if (moods.isEmpty()) {
            return MoodStatistics(
                totalEntries = 0,
                averageMood = 0.0,
                currentStreak = 0,
                longestStreak = 0,
                moodDistribution = emptyMap(),
                weeklyAverage = emptyMap(),
                monthlyTrend = emptyList(),
                moodFrequency = emptyMap()
            )
        }
        
        val sortedMoods = moods.sortedBy { it.date }
        
        return MoodStatistics(
            totalEntries = moods.size,
            averageMood = moods.map { it.mood }.average(),
            currentStreak = calculateCurrentStreak(sortedMoods),
            longestStreak = calculateLongestStreak(sortedMoods),
            moodDistribution = calculateMoodDistribution(moods),
            weeklyAverage = calculateWeeklyAverage(moods),
            monthlyTrend = calculateMonthlyTrend(moods),
            moodFrequency = calculateMoodFrequency(moods)
        )
    }
    
    private fun calculateCurrentStreak(sortedMoods: List<MoodEntity>): Int {
        if (sortedMoods.isEmpty()) return 0
        
        var streak = 0
        var currentDate = LocalDate.now()
        
        // 从今天开始往前计算连续打卡天数
        for (mood in sortedMoods.reversed()) {
            if (mood.date == currentDate) {
                streak++
                currentDate = currentDate.minusDays(1)
            } else {
                break
            }
        }
        
        return streak
    }
    
    private fun calculateLongestStreak(sortedMoods: List<MoodEntity>): Int {
        if (sortedMoods.isEmpty()) return 0
        
        var longestStreak = 1
        var currentStreak = 1
        
        for (i in 1 until sortedMoods.size) {
            val prevDate = sortedMoods[i - 1].date
            val currentDate = sortedMoods[i].date
            
            if (ChronoUnit.DAYS.between(prevDate, currentDate) == 1L) {
                currentStreak++
                longestStreak = maxOf(longestStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }
        
        return longestStreak
    }
    
    private fun calculateMoodDistribution(moods: List<MoodEntity>): Map<MoodLevel, Int> {
        return MoodLevel.values().associateWith { level ->
            moods.count { it.mood == level.value }
        }
    }
    
    private fun calculateWeeklyAverage(moods: List<MoodEntity>): Map<DayOfWeek, Double> {
        val moodsByDayOfWeek = moods.groupBy { it.date.dayOfWeek }
        
        return DayOfWeek.values().associateWith { dayOfWeek ->
            val dayMoods = moodsByDayOfWeek[dayOfWeek] ?: emptyList()
            if (dayMoods.isNotEmpty()) {
                dayMoods.map { it.mood }.average()
            } else {
                0.0
            }
        }
    }
    
    private fun calculateMonthlyTrend(moods: List<MoodEntity>): List<Pair<String, Double>> {
        val moodsByMonth = moods.groupBy { 
            "${it.date.year}-${it.date.monthValue.toString().padStart(2, '0')}"
        }
        
        return moodsByMonth.map { (month, monthMoods) ->
            month to monthMoods.map { it.mood }.average()
        }.sortedBy { it.first }
    }
    
    private fun calculateMoodFrequency(moods: List<MoodEntity>): Map<MoodLevel, Double> {
        val total = moods.size.toDouble()
        return MoodLevel.values().associateWith { level ->
            if (total > 0) {
                moods.count { it.mood == level.value } / total * 100
            } else {
                0.0
            }
        }
    }
    
    private fun calculateWeeklyStats(moods: List<MoodEntity>, startDate: LocalDate): List<WeeklyStats> {
        val weeks = mutableListOf<WeeklyStats>()
        var currentWeekStart = startDate
        
        // 计算最近8周的统计
        repeat(8) {
            val weekEnd = currentWeekStart.plusDays(6)
            val weekMoods = moods.filter { it.date in currentWeekStart..weekEnd }
            
            val averageMood = if (weekMoods.isNotEmpty()) {
                weekMoods.map { it.mood }.average()
            } else {
                0.0
            }
            
            val dominantMood = if (weekMoods.isNotEmpty()) {
                val moodCounts = weekMoods.groupBy { it.mood }
                val maxCount = moodCounts.maxByOrNull { it.value.size }
                maxCount?.let { MoodLevel.fromValue(it.key) }
            } else {
                null
            }
            
            weeks.add(
                WeeklyStats(
                    weekStart = currentWeekStart,
                    weekEnd = weekEnd,
                    averageMood = averageMood,
                    totalEntries = weekMoods.size,
                    dominantMood = dominantMood
                )
            )
            
            currentWeekStart = currentWeekStart.minusWeeks(1)
        }
        
        return weeks.reversed()
    }
    
    private fun calculateMonthlyStats(moods: List<MoodEntity>, year: Int): List<MonthlyStats> {
        val months = (1..12).map { month ->
            val monthMoods = moods.filter { 
                it.date.year == year && it.date.monthValue == month 
            }
            
            val averageMood = if (monthMoods.isNotEmpty()) {
                monthMoods.map { it.mood }.average()
            } else {
                0.0
            }
            
            val bestDay = monthMoods.maxByOrNull { it.mood }?.date
            val worstDay = monthMoods.minByOrNull { it.mood }?.date
            
            MonthlyStats(
                month = getMonthName(month),
                year = year,
                averageMood = averageMood,
                totalEntries = monthMoods.size,
                bestDay = bestDay,
                worstDay = worstDay
            )
        }
        
        return months
    }
    
    private fun getMonthName(month: Int): String {
        return when (month) {
            1 -> "一月"
            2 -> "二月"
            3 -> "三月"
            4 -> "四月"
            5 -> "五月"
            6 -> "六月"
            7 -> "七月"
            8 -> "八月"
            9 -> "九月"
            10 -> "十月"
            11 -> "十一月"
            12 -> "十二月"
            else -> "未知"
        }
    }
}
