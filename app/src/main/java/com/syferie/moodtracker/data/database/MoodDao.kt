package com.syferie.moodtracker.data.database

import androidx.room.*
import com.syferie.moodtracker.data.model.MoodEntity
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

@Dao
interface MoodDao {
    
    @Query("SELECT * FROM mood ORDER BY date DESC")
    fun getAllMoods(): Flow<List<MoodEntity>>
    
    @Query("SELECT * FROM mood WHERE date = :date")
    suspend fun getMoodByDate(date: LocalDate): MoodEntity?
    
    @Query("SELECT * FROM mood WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    fun getMoodsBetweenDates(startDate: LocalDate, endDate: LocalDate): Flow<List<MoodEntity>>
    
    @Query("SELECT * FROM mood WHERE date >= :startDate ORDER BY date ASC")
    fun getMoodsFromDate(startDate: LocalDate): Flow<List<MoodEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMood(mood: MoodEntity)
    
    @Update
    suspend fun updateMood(mood: MoodEntity)
    
    @Delete
    suspend fun deleteMood(mood: MoodEntity)
    
    @Query("DELETE FROM mood WHERE date = :date")
    suspend fun deleteMoodByDate(date: LocalDate)
    
    @Query("SELECT COUNT(*) FROM mood")
    suspend fun getMoodCount(): Int
    
    @Query("SELECT AVG(mood) FROM mood WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getAverageMoodBetweenDates(startDate: LocalDate, endDate: LocalDate): Double?
}
