package com.syferie.moodtracker.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.syferie.moodtracker.data.model.MoodEntity

@Database(
    entities = [MoodEntity::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class MoodDatabase : RoomDatabase() {
    
    abstract fun moodDao(): MoodDao
    
    companion object {
        const val DATABASE_NAME = "mood_database"
    }
}
