package com.syferie.moodtracker.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDate

@Entity(tableName = "mood")
data class MoodEntity(
    @PrimaryKey val date: LocalDate,
    val mood: Int,           // 0–4 对应五档情绪
    val note: String?
)

// 情绪枚举类 - 扩展到12种可爱心情
enum class MoodLevel(val value: Int, val emoji: String, val description: String, val iconRes: String) {
    VERY_ANGRY(0, "😡", "纠结愁容", "00"),
    ANGRY(1, "😠", "微怒于心", "01"),
    VERY_SAD(2, "😭", "甜蜜时光", "02"),
    SAD(3, "😢", "开心嗨皮", "03"),
    WORRIED(4, "😰", "尴尬忧思", "04"),
    NEUTRAL(5, "😐", "伤心难过", "05"),
    CALM(6, "😌", "挑战好奇", "06"),
    HAPPY(7, "😊", "收获满满", "07"),
    VERY_HAPPY(8, "😄", "耀眼自豪", "08"),
    EXCITED(9, "🤩", "哎呦烦人", "09"),
    LOVE(10, "😍", "muad害怕", "10"),
    BLISSFUL(11, "🥰", "疲倦无力", "11");

    companion object {
        fun fromValue(value: Int): MoodLevel {
            return values().find { it.value == value } ?: NEUTRAL
        }

        // 获取drawable资源名称
        fun getDrawableResourceName(moodLevel: MoodLevel): String {
            return "mood_${moodLevel.iconRes}"
        }
    }
}
