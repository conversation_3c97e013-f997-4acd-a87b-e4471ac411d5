package com.syferie.moodtracker.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDate

@Entity(tableName = "mood")
data class MoodEntity(
    @PrimaryKey val date: LocalDate,
    val mood: Int,           // 0–4 对应五档情绪
    val note: String?
)

// 情绪枚举类 - 扩展到12种可爱心情
enum class MoodLevel(val value: Int, val emoji: String, val description: String, val iconRes: String) {
    VERY_ANGRY(0, "😡", "非常愤怒", "00"),
    ANGRY(1, "😠", "愤怒", "01"),
    VERY_SAD(2, "😭", "非常难过", "02"),
    SAD(3, "😢", "难过", "03"),
    WORRIED(4, "😰", "担心", "04"),
    NEUTRAL(5, "😐", "一般", "05"),
    CALM(6, "😌", "平静", "06"),
    HAPPY(7, "😊", "开心", "07"),
    VERY_HAPPY(8, "😄", "非常开心", "08"),
    EXCITED(9, "🤩", "兴奋", "09"),
    LOVE(10, "😍", "喜爱", "10"),
    BLISSFUL(11, "🥰", "幸福", "11");

    companion object {
        fun fromValue(value: Int): MoodLevel {
            return values().find { it.value == value } ?: NEUTRAL
        }

        // 获取drawable资源名称
        fun getDrawableResourceName(moodLevel: MoodLevel): String {
            return "mood_${moodLevel.iconRes}"
        }
    }
}
