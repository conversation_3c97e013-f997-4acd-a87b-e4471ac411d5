# MoodTracker - 心情打卡应用

一个基于 Kotlin + Jetpack Compose 的现代化心情记录应用，帮助用户追踪和分析日常心情变化。

## 📱 应用特性

### 核心功能
- **心情打卡**: 12种可爱情绪记录，从愤怒到幸福的完整情感光谱
- **日历视图**: 直观的月历显示，可爱图标标记每日心情
- **统计分析**: 多维度数据分析和个性化洞察
- **数据导出**: 支持JSON、CSV和统计报告导出

### 用户体验
- **可爱画风设计**: 12种精美心情图标，温馨可爱的视觉体验
- **流畅动画**: 优化的页面切换和微交互动画
- **响应式设计**: 适配不同屏幕尺寸
- **直观操作**: 网格布局的心情选择，简单易用

## 🏗️ 技术架构

### 核心技术栈
- **Kotlin**: 100% Kotlin 开发
- **Jetpack Compose**: 现代化声明式UI
- **MVVM架构**: 清晰的代码结构
- **Room数据库**: 本地数据持久化
- **Hilt依赖注入**: 模块化依赖管理

### 主要组件
- **数据层**: Room + Repository模式
- **业务层**: ViewModel + UseCase
- **UI层**: Compose + Navigation
- **动画系统**: Compose Animation API

## 📊 功能模块

### 1. 心情记录
- 12种可爱心情图标选择
- 网格布局的直观选择界面
- 文字备注支持
- 历史记录管理
- 快速编辑功能

### 2. 数据可视化
- 心情分布图表
- 趋势分析曲线
- 周/月统计视图
- 个性化洞察

### 3. 统计分析
- 连续打卡统计
- 心情模式识别
- 时间趋势分析
- 智能建议生成

### 4. 数据管理
- 多格式数据导出
- 统计报告生成
- 数据备份功能
- 隐私保护

## 🎨 设计特色

### Material You
- 动态颜色适配
- 系统主题跟随
- 现代化卡片设计
- 一致的视觉语言

### 动画效果
- 心情选择动画
- 页面转场效果
- 数据加载动画
- 成功反馈动画

### 颜色系统
- 心情专属颜色
- 渐变过渡效果
- 高对比度支持
- 无障碍设计

## 📈 开发历程

项目采用分阶段开发方式，确保每个阶段都有完整的功能和文档：

### 阶段1: 基础架构 ✅
- MVVM架构搭建
- Room数据库配置
- Hilt依赖注入
- 基础数据模型

### 阶段2: 核心功能 ✅
- 心情打卡界面
- 日历视图实现
- 底部导航结构
- 基础统计功能

### 阶段3: UI优化 ✅
- Material You主题
- 动画系统实现
- 用户反馈机制
- 视觉效果增强

### 阶段4: 统计增强 ✅
- 高级数据分析
- 个性化洞察
- 多维度统计
- 图表可视化

### 阶段5: 扩展功能 ✅
- 数据导出系统
- 设置页面完善
- 文件分享功能
- 用户偏好管理

### 阶段6: UI重构升级 ✅
- 心情类型扩展到12种
- 可爱画风图标系统
- 网格布局心情选择
- 日历界面视觉升级
- 页面切换性能优化

### 阶段7: 性能优化 ✅
- 底部导航切换性能优化
- ViewModel数据流优化
- 组件重组性能提升
- 导航动画流畅度改进
- 性能监控工具集成

## 🛠️ 开发环境

### 要求
- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Android SDK 34+
- JDK 17+

### 依赖版本
- Compose BOM: 2024.02.00
- Room: 2.6.1
- Hilt: 2.51
- Navigation: 2.8.0

## 📱 安装使用

### 构建应用
```bash
git clone https://github.com/your-username/MoodTracker.git
cd MoodTracker
./gradlew assembleDebug
```

### 运行测试
```bash
./gradlew test
./gradlew connectedAndroidTest
```

## 📄 项目文档

详细的开发文档位于 `dev_docs/` 目录：
- `app.md` - 应用架构设计
- `requirement.md` - 需求分析
- `stage1_architecture.md` - 基础架构文档
- `stage2_mvp_core.md` - 核心功能文档
- `stage3_ui_animations.md` - UI动画文档
- `stage4_enhanced_stats.md` - 统计功能文档
- `stage5_settings_export.md` - 设置导出文档
- `ui_refactor_cute_style.md` - UI重构可爱画风升级文档
- `navigation_performance_optimization.md` - 导航性能优化文档

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发规范
- 遵循Kotlin编码规范
- 使用Compose最佳实践
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有开源项目和社区的支持：
- Android Jetpack团队
- Compose开发团队
- Material Design团队
- Kotlin开发团队

---

**MoodTracker** - 记录心情，发现生活的美好 ❤️
